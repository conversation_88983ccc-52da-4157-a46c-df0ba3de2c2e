Log started at 7/6/2025 4:46:21 PM
2025-07-06 16:46:21.789 [Information] LoggingService: Logging service initialized
2025-07-06 16:46:21.803 [Information] App: Starting integrated application initialization
2025-07-06 16:46:21.806 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 16:46:21.809 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 16:46:21.812 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 16:46:21.813 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 16:46:21.815 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 16:46:21.817 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 16:46:21.821 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 16:46:21.830 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 16:46:21.833 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:46:21.836 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:46:21.837 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 16:46:21.841 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 16:46:21.844 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:46:21.847 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:46:21.848 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:46:21.852 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 16:46:21.856 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:46:21.859 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:46:21.860 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:46:21.864 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 16:46:21.867 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:46:21.871 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:46:21.872 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:46:21.875 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 16:46:21.878 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 16:46:21.879 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 16:46:21.881 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 16:46:21.881 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 16:46:21.882 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 16:46:21.883 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 16:46:21.883 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 16:46:21.884 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 16:46:21.884 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 16:46:21.885 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 16:46:21.886 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:46:21.886 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:46:21.887 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:46:21.896 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 16:46:21.897 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 16:46:21.898 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 16:46:21.907 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 16:46:21.908 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 16:46:21.910 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 16:46:21.913 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 16:46:21.918 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 16:46:21.920 [Information] LibraryExtractor: Copying system libraries
2025-07-06 16:46:21.927 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 16:46:21.936 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 16:46:48.709 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 16:47:39.880 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 16:48:27.247 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 16:49:16.475 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:50:04.576 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:50:53.176 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:51:52.178 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 16:51:52.179 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 16:51:52.180 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 16:51:52.181 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 16:51:52.181 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 16:51:52.182 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 16:51:52.188 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 16:51:52.192 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 16:51:52.194 [Information] DependencyManager: Initializing dependency manager
2025-07-06 16:51:52.195 [Information] DependencyManager: Setting up library search paths
2025-07-06 16:51:52.197 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:51:52.197 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:51:52.198 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 16:51:52.198 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 16:51:52.200 [Information] DependencyManager: Verifying required directories
2025-07-06 16:51:52.201 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:51:52.201 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:51:52.201 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 16:51:52.202 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 16:51:52.204 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 16:51:52.212 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 16:51:52.214 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 16:51:52.217 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 16:51:52.219 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 16:51:52.222 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 16:51:52.223 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:51:52.224 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:51:52.225 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:51:52.227 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 16:51:52.229 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 16:51:52.229 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 16:51:52.230 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 16:51:52.230 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 16:51:52.231 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 16:51:52.232 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 16:51:52.232 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 16:51:52.233 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 16:51:52.234 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 16:51:52.234 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 16:51:52.235 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 16:51:52.237 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 16:51:52.238 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 16:51:52.238 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 16:51:52.239 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 16:51:52.240 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 16:51:52.240 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 16:51:52.241 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 16:51:52.241 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 16:51:52.242 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 16:51:52.242 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 16:51:52.243 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 16:51:52.243 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 16:51:52.244 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 16:51:52.245 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 16:51:52.246 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 16:51:52.247 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 16:51:52.247 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 16:51:52.248 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 16:51:52.249 [Information] DependencyManager: Setting up environment variables
2025-07-06 16:51:52.250 [Information] DependencyManager: Environment variables configured
2025-07-06 16:51:52.251 [Information] DependencyManager: Verifying library loading status
2025-07-06 16:51:52.582 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 16:51:52.583 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 16:51:52.584 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 16:51:52.586 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 16:51:52.589 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 16:51:52.592 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 16:51:52.594 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 16:51:52.595 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 16:51:52.595 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 16:51:52.597 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 16:51:52.597 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 16:51:52.598 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:51:52.598 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:51:52.598 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 16:51:52.599 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 16:51:52.599 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 16:51:52.599 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:51:52.600 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 16:51:52.600 [Information] App: Integrated startup completed successfully
2025-07-06 16:51:52.605 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 16:51:52.617 [Information] App: Initializing application services
2025-07-06 16:51:52.619 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 16:51:52.620 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 16:51:52.664 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 16:51:52.665 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 16:51:52.666 [Information] App: Configuration service initialized successfully
2025-07-06 16:51:52.667 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 16:51:52.668 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 16:51:52.675 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 16:51:52.676 [Information] App: Final useDummyImplementations value: False
2025-07-06 16:51:52.676 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 16:51:52.678 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 16:51:52.693 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 16:51:52.694 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 16:51:52.695 [Information] App: usePatchedImplementation flag is: True
2025-07-06 16:51:52.695 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 16:51:52.695 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 16:51:52.696 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 16:51:52.696 [Information] App: verboseLogging flag is: True
2025-07-06 16:51:52.698 [Information] App: Verifying real hardware requirements...
2025-07-06 16:51:52.699 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 16:51:52.699 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 16:51:52.700 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 16:51:52.700 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 16:51:52.701 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 16:51:52.701 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 16:51:52.701 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 16:51:52.702 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 16:51:52.714 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 16:51:52.717 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 16:51:52.720 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 16:51:52.724 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 16:51:52.724 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 16:51:52.725 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 16:51:52.726 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 16:51:52.726 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 16:51:52.726 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 16:51:52.727 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 16:51:52.727 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 16:51:52.727 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 16:51:52.729 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 16:51:52.729 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 16:51:52.731 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 16:51:52.731 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 16:51:52.732 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 16:51:52.732 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 16:51:52.734 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 16:51:52.735 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 16:51:52.736 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 16:51:52.738 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:51:52.742 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:51:52.829 [Information] VocomArchitectureBridge: Started bridge process with PID 14140
2025-07-06 16:51:53.830 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 16:51:53.835 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 16:51:53.944 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 16:51:53.945 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:51:53.947 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 16:51:53.948 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 16:51:53.948 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:51:53.948 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:51:53.949 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 16:51:53.949 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:51:53.949 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 16:51:53.950 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 16:51:53.950 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 16:51:54.007 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:51:54.077 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:51:54.079 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:51:54.079 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 16:51:54.084 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 16:51:54.091 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:54.091 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 16:51:54.097 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:51:54.099 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:51:54.100 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:51:54.104 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:51:54.107 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:51:54.112 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:51:54.116 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:51:54.119 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:51:54.131 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:51:54.134 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:51:54.134 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:54.140 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:51:54.140 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:51:54.141 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:54.144 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:51:54.144 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:51:54.145 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:54.148 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:51:54.148 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:51:54.149 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:54.151 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:51:54.152 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:51:54.152 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:54.153 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:51:54.158 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:51:54.160 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:54.161 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:51:54.161 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:51:54.162 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:54.162 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:51:54.163 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:51:54.163 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:54.164 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:51:54.164 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:51:54.165 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:54.165 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:51:54.166 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:51:54.166 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:51:54.169 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:51:54.173 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:51:54.173 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:51:54.174 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:51:54.175 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:51:54.176 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 16:51:55.176 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 16:51:55.177 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:51:55.178 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:51:55.178 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:51:55.179 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:51:55.179 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:51:55.180 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:51:55.180 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:51:55.182 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:51:55.182 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:51:55.182 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:51:55.183 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:55.183 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:51:55.183 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:51:55.184 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:55.184 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:51:55.184 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:51:55.185 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:55.185 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:51:55.185 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:51:55.186 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:55.186 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:51:55.186 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:51:55.186 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:55.187 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:51:55.187 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:51:55.188 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:55.189 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:51:55.189 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:51:55.189 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:55.190 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:51:55.192 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:51:55.192 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:55.193 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:51:55.193 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:51:55.193 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:55.194 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:51:55.194 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:51:55.194 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:51:55.195 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:51:55.196 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:51:55.196 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:51:55.197 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:51:55.197 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:51:55.197 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 16:51:57.197 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 16:51:57.198 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:51:57.199 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:51:57.199 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:51:57.199 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:51:57.200 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:51:57.201 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:51:57.203 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:51:57.204 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:51:57.207 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:51:57.207 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:51:57.207 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:57.208 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:51:57.208 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:51:57.208 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:57.209 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:51:57.209 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:51:57.210 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:57.211 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:51:57.211 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:51:57.212 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:57.212 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:51:57.212 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:51:57.213 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:51:57.213 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:51:57.213 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:51:57.214 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:57.214 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:51:57.214 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:51:57.215 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:57.215 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:51:57.216 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:51:57.216 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:57.216 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:51:57.217 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:51:57.218 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:51:57.219 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:51:57.219 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:51:57.219 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:51:57.220 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:51:57.221 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:51:57.222 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:51:57.223 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:51:57.223 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:51:57.224 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 16:52:00.224 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 16:52:00.227 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 16:52:00.228 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 16:52:00.231 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 16:52:00.732 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 16:52:00.732 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 16:52:00.734 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 16:52:00.735 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 16:52:00.739 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 16:52:00.742 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 16:52:00.746 [Information] BackupService: Initializing backup service
2025-07-06 16:52:00.747 [Information] BackupService: Backup service initialized successfully
2025-07-06 16:52:00.747 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 16:52:00.747 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 16:52:00.750 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 16:52:00.786 [Information] BackupService: Compressing backup data
2025-07-06 16:52:00.800 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-06 16:52:00.802 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 16:52:00.803 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 16:52:00.803 [Information] BackupService: Compressing backup data
2025-07-06 16:52:00.805 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-06 16:52:00.805 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 16:52:00.806 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 16:52:00.807 [Information] BackupService: Compressing backup data
2025-07-06 16:52:00.809 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-07-06 16:52:00.809 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 16:52:00.811 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 16:52:00.812 [Information] BackupService: Compressing backup data
2025-07-06 16:52:00.813 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-06 16:52:00.813 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 16:52:00.814 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 16:52:00.814 [Information] BackupService: Compressing backup data
2025-07-06 16:52:00.815 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (448 bytes)
2025-07-06 16:52:00.815 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 16:52:00.816 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 16:52:00.816 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 16:52:00.817 [Information] BackupService: Compressing backup data
2025-07-06 16:52:00.819 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-06 16:52:00.819 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 16:52:00.820 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 16:52:00.825 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 16:52:00.829 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 16:52:00.832 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 16:52:00.904 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 16:52:00.905 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 16:52:00.908 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 16:52:00.908 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 16:52:00.909 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 16:52:00.910 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 16:52:00.911 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 16:52:00.915 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 16:52:00.915 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 16:52:00.927 [Information] LicensingService: Initializing licensing service
2025-07-06 16:52:00.973 [Information] LicensingService: License information loaded successfully
2025-07-06 16:52:00.978 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 16:52:00.978 [Information] App: Licensing service initialized successfully
2025-07-06 16:52:00.979 [Information] App: License status: Trial
2025-07-06 16:52:00.982 [Information] App: Trial period: 29 days remaining
2025-07-06 16:52:00.983 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 16:52:01.160 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:52:01.161 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:52:01.161 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 16:52:01.161 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:52:01.212 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 16:52:01.712 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 16:52:01.763 [Information] BackupService: Initializing backup service
2025-07-06 16:52:01.763 [Information] BackupService: Backup service initialized successfully
2025-07-06 16:52:01.815 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 16:52:01.815 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 16:52:01.817 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 16:52:01.817 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 16:52:01.869 [Information] BackupService: Getting predefined backup categories
2025-07-06 16:52:01.921 [Information] MainViewModel: Services initialized successfully
2025-07-06 16:52:01.925 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 16:52:01.927 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:52:01.942 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:52:01.943 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:52:01.944 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 16:52:38.908 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 16:52:38.908 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:52:38.915 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:52:38.916 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:52:38.916 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 16:54:52.241 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 16:54:52.242 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:54:52.406 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:54:52.406 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:54:52.407 [Information] MainViewModel: Found 0 Vocom device(s)
