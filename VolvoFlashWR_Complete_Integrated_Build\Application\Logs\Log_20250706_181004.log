Log started at 7/6/2025 6:10:04 PM
2025-07-06 18:10:04.439 [Information] LoggingService: Logging service initialized
2025-07-06 18:10:04.457 [Information] App: Starting integrated application initialization
2025-07-06 18:10:04.459 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 18:10:04.464 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 18:10:04.466 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 18:10:04.468 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 18:10:04.470 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 18:10:04.473 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 18:10:04.476 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 18:10:04.486 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 18:10:04.489 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 18:10:04.492 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 18:10:04.493 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 18:10:04.497 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 18:10:04.500 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 18:10:04.503 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 18:10:04.504 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 18:10:04.508 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 18:10:04.515 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 18:10:04.518 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 18:10:04.518 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 18:10:04.522 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 18:10:04.525 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 18:10:04.528 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 18:10:04.529 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 18:10:04.532 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 18:10:04.534 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 18:10:04.535 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 18:10:04.537 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 18:10:04.537 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 18:10:04.538 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 18:10:04.538 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 18:10:04.539 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 18:10:04.539 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 18:10:04.540 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 18:10:04.541 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 18:10:04.541 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 18:10:04.541 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 18:10:04.542 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 18:10:04.550 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 18:10:04.550 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 18:10:04.551 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 18:10:04.557 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 18:10:04.558 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 18:10:04.560 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 18:10:04.562 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 18:10:04.566 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 18:10:04.569 [Information] LibraryExtractor: Copying system libraries
2025-07-06 18:10:04.575 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 18:10:04.584 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 18:10:30.426 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 18:11:21.423 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 18:12:15.271 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 18:13:05.129 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 18:13:56.657 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 18:14:41.219 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
