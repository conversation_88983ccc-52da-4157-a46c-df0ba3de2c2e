Log started at 7/6/2025 5:25:03 PM
2025-07-06 17:25:03.444 [Information] LoggingService: Logging service initialized
2025-07-06 17:25:03.474 [Information] App: Starting integrated application initialization
2025-07-06 17:25:03.477 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 17:25:03.484 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 17:25:03.489 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 17:25:03.490 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 17:25:03.493 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 17:25:03.495 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 17:25:03.500 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 17:25:03.510 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:25:03.513 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:25:03.516 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:25:03.517 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 17:25:03.522 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:25:03.525 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:25:03.528 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:25:03.528 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:25:03.532 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:25:03.536 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:25:03.540 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:25:03.540 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:25:03.545 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:25:03.548 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:25:03.551 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:25:03.552 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:25:03.555 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 17:25:03.558 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 17:25:03.559 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 17:25:03.562 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 17:25:03.563 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 17:25:03.565 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 17:25:03.566 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 17:25:03.566 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 17:25:03.569 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 17:25:03.570 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 17:25:03.571 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 17:25:03.572 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:25:03.573 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:25:03.573 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:25:03.579 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 17:25:03.580 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 17:25:03.581 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 17:25:03.590 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 17:25:03.590 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 17:25:03.592 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 17:25:03.595 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 17:25:03.598 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 17:25:03.601 [Information] LibraryExtractor: Copying system libraries
2025-07-06 17:25:03.608 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 17:25:03.621 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 17:25:29.956 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 17:26:22.270 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 17:27:09.269 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 17:28:01.855 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:28:50.214 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:29:45.319 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:30:31.812 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 17:30:31.813 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 17:30:31.816 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 17:30:31.816 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 17:30:31.817 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 17:30:31.817 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 17:30:31.824 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 17:30:31.826 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 17:30:31.828 [Information] DependencyManager: Initializing dependency manager
2025-07-06 17:30:31.833 [Information] DependencyManager: Setting up library search paths
2025-07-06 17:30:31.834 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:30:31.835 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:30:31.836 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 17:30:31.837 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 17:30:31.839 [Information] DependencyManager: Verifying required directories
2025-07-06 17:30:31.840 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:30:31.840 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:30:31.841 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 17:30:31.841 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 17:30:31.843 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 17:30:31.853 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 17:30:31.855 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 17:30:31.858 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 17:30:31.862 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 17:30:31.866 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 17:30:31.867 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:30:31.868 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:30:31.869 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:30:31.871 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 17:30:31.873 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 17:30:31.873 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 17:30:31.874 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 17:30:31.875 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 17:30:31.876 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 17:30:31.878 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 17:30:31.879 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 17:30:31.880 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 17:30:31.882 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 17:30:31.883 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 17:30:31.884 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 17:30:31.885 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 17:30:31.885 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 17:30:31.886 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 17:30:31.887 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 17:30:31.888 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 17:30:31.889 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 17:30:31.890 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 17:30:31.891 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 17:30:31.891 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 17:30:31.893 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 17:30:31.893 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 17:30:31.894 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 17:30:31.895 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 17:30:31.896 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 17:30:31.896 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 17:30:31.898 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 17:30:31.899 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 17:30:31.900 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 17:30:31.901 [Information] DependencyManager: Setting up environment variables
2025-07-06 17:30:31.901 [Information] DependencyManager: Environment variables configured
2025-07-06 17:30:31.903 [Information] DependencyManager: Verifying library loading status
2025-07-06 17:30:32.283 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 17:30:32.284 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 17:30:32.284 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 17:30:32.288 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 17:30:32.290 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 17:30:32.295 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 17:30:32.298 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 17:30:32.299 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 17:30:32.299 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 17:30:32.301 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 17:30:32.301 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 17:30:32.302 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:30:32.302 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:30:32.302 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 17:30:32.303 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 17:30:32.303 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 17:30:32.303 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:30:32.304 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 17:30:32.304 [Information] App: Integrated startup completed successfully
2025-07-06 17:30:32.307 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 17:30:32.326 [Information] App: Initializing application services
2025-07-06 17:30:32.329 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 17:30:32.329 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 17:30:32.378 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 17:30:32.379 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 17:30:32.381 [Information] App: Configuration service initialized successfully
2025-07-06 17:30:32.383 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 17:30:32.383 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 17:30:32.389 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 17:30:32.390 [Information] App: Final useDummyImplementations value: False
2025-07-06 17:30:32.390 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 17:30:32.392 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 17:30:32.410 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 17:30:32.413 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 17:30:32.416 [Information] App: usePatchedImplementation flag is: True
2025-07-06 17:30:32.416 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 17:30:32.417 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 17:30:32.417 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 17:30:32.417 [Information] App: verboseLogging flag is: True
2025-07-06 17:30:32.421 [Information] App: Verifying real hardware requirements...
2025-07-06 17:30:32.421 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 17:30:32.423 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 17:30:32.423 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 17:30:32.423 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 17:30:32.424 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 17:30:32.424 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 17:30:32.425 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 17:30:32.425 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 17:30:32.436 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 17:30:32.441 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 17:30:32.445 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 17:30:32.450 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 17:30:32.450 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 17:30:32.451 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 17:30:32.451 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 17:30:32.452 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 17:30:32.452 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 17:30:32.453 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 17:30:32.453 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 17:30:32.453 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 17:30:32.455 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 17:30:32.455 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 17:30:32.459 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 17:30:32.460 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 17:30:32.461 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 17:30:32.461 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 17:30:32.467 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 17:30:32.467 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 17:30:32.468 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 17:30:32.471 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:30:32.476 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:30:32.489 [Information] VocomArchitectureBridge: Started bridge process with PID 12432
2025-07-06 17:30:33.491 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 17:30:33.496 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 17:30:33.601 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 17:30:33.602 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:30:33.602 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 17:30:33.603 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 17:30:33.603 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:30:33.603 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:30:33.604 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 17:30:33.606 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:30:33.606 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 17:30:33.607 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 17:30:33.607 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 17:30:33.660 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:30:33.738 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:30:33.739 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:30:33.740 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 17:30:33.744 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 17:30:33.748 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:33.749 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 17:30:33.754 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:30:33.756 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:30:33.756 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:30:33.760 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:30:33.762 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:30:33.779 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:30:33.791 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:30:33.799 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:30:33.811 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:30:33.826 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:30:33.827 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:33.831 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:30:33.832 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:30:33.832 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:33.835 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:30:33.836 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:30:33.836 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:33.838 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:30:33.839 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:30:33.839 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:33.842 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:30:33.842 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:30:33.843 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:33.843 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:30:33.849 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:30:33.851 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:33.852 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:30:33.852 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:30:33.853 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:33.853 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:30:33.853 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:30:33.854 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:33.854 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:30:33.855 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:30:33.855 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:33.856 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:30:33.856 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:30:33.857 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:30:33.858 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:30:33.862 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:30:33.863 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:30:33.864 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:30:33.864 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:30:33.866 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 17:30:34.867 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 17:30:34.868 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:30:34.870 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:30:34.870 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:30:34.871 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:30:34.871 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:30:34.875 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:30:34.875 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:30:34.877 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:30:34.878 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:30:34.878 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:30:34.879 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:34.880 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:30:34.880 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:30:34.883 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:34.884 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:30:34.885 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:30:34.885 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:34.885 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:30:34.886 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:30:34.886 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:34.886 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:30:34.886 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:30:34.887 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:34.887 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:30:34.888 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:30:34.888 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:34.889 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:30:34.889 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:30:34.889 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:34.890 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:30:34.891 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:30:34.891 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:34.892 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:30:34.892 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:30:34.893 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:34.896 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:30:34.900 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:30:34.900 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:30:34.900 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:30:34.904 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:30:34.904 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:30:34.905 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:30:34.905 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:30:34.905 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 17:30:36.906 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 17:30:36.907 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:30:36.908 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:30:36.908 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:30:36.909 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:30:36.909 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:30:36.910 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:30:36.910 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:30:36.911 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:30:36.911 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:30:36.911 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:30:36.912 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:36.912 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:30:36.913 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:30:36.913 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:36.914 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:30:36.914 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:30:36.914 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:36.915 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:30:36.916 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:30:36.917 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:36.917 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:30:36.917 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:30:36.918 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:30:36.918 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:30:36.918 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:30:36.919 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:36.919 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:30:36.920 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:30:36.920 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:36.920 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:30:36.921 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:30:36.921 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:36.921 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:30:36.922 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:30:36.922 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:30:36.922 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:30:36.923 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:30:36.923 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:30:36.923 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:30:36.925 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:30:36.925 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:30:36.926 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:30:36.926 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:30:36.926 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 17:30:39.926 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 17:30:39.927 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 17:30:39.928 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 17:30:39.930 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 17:30:40.431 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 17:30:40.432 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 17:30:40.433 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 17:30:40.433 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 17:30:40.438 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 17:30:40.440 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 17:30:40.443 [Information] BackupService: Initializing backup service
2025-07-06 17:30:40.443 [Information] BackupService: Backup service initialized successfully
2025-07-06 17:30:40.444 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 17:30:40.444 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 17:30:40.447 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 17:30:40.473 [Information] BackupService: Compressing backup data
2025-07-06 17:30:40.482 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-06 17:30:40.485 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 17:30:40.486 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 17:30:40.486 [Information] BackupService: Compressing backup data
2025-07-06 17:30:40.488 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (453 bytes)
2025-07-06 17:30:40.489 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 17:30:40.489 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 17:30:40.490 [Information] BackupService: Compressing backup data
2025-07-06 17:30:40.491 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (442 bytes)
2025-07-06 17:30:40.491 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 17:30:40.492 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 17:30:40.492 [Information] BackupService: Compressing backup data
2025-07-06 17:30:40.493 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-07-06 17:30:40.494 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 17:30:40.494 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 17:30:40.495 [Information] BackupService: Compressing backup data
2025-07-06 17:30:40.496 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (448 bytes)
2025-07-06 17:30:40.496 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 17:30:40.496 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 17:30:40.497 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 17:30:40.498 [Information] BackupService: Compressing backup data
2025-07-06 17:30:40.499 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-06 17:30:40.500 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 17:30:40.500 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 17:30:40.503 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 17:30:40.507 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 17:30:40.510 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 17:30:40.577 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 17:30:40.578 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 17:30:40.580 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 17:30:40.580 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 17:30:40.581 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 17:30:40.583 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 17:30:40.585 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 17:30:40.589 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 17:30:40.590 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 17:30:40.599 [Information] LicensingService: Initializing licensing service
2025-07-06 17:30:40.649 [Information] LicensingService: License information loaded successfully
2025-07-06 17:30:40.653 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 17:30:40.653 [Information] App: Licensing service initialized successfully
2025-07-06 17:30:40.654 [Information] App: License status: Trial
2025-07-06 17:30:40.657 [Information] App: Trial period: 29 days remaining
2025-07-06 17:30:40.658 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 17:30:40.837 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:30:40.837 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:30:40.837 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 17:30:40.837 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:30:40.889 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 17:30:41.391 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 17:30:41.442 [Information] BackupService: Initializing backup service
2025-07-06 17:30:41.442 [Information] BackupService: Backup service initialized successfully
2025-07-06 17:30:41.493 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 17:30:41.494 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 17:30:41.496 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 17:30:41.497 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 17:30:41.549 [Information] BackupService: Getting predefined backup categories
2025-07-06 17:30:41.601 [Information] MainViewModel: Services initialized successfully
2025-07-06 17:30:41.604 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 17:30:41.606 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:30:41.622 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:30:41.623 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:30:41.624 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 17:34:05.456 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 17:34:05.457 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:34:05.466 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:34:05.466 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:34:05.467 [Information] MainViewModel: Found 0 Vocom device(s)
