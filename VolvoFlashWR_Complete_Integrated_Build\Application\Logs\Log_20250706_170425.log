Log started at 7/6/2025 5:04:25 PM
2025-07-06 17:04:25.588 [Information] LoggingService: Logging service initialized
2025-07-06 17:04:25.612 [Information] App: Starting integrated application initialization
2025-07-06 17:04:25.614 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 17:04:25.622 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 17:04:25.625 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 17:04:25.626 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 17:04:25.628 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 17:04:25.631 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 17:04:25.636 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 17:04:25.647 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:04:25.650 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:04:25.654 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:04:25.655 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 17:04:25.660 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:04:25.663 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:04:25.667 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:04:25.668 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:04:25.673 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:04:25.676 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:04:25.679 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:04:25.680 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:04:25.685 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:04:25.689 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:04:25.692 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:04:25.693 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:04:25.696 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 17:04:25.699 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 17:04:25.700 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 17:04:25.702 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 17:04:25.702 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 17:04:25.704 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 17:04:25.705 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 17:04:25.706 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 17:04:25.707 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 17:04:25.707 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 17:04:25.708 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 17:04:25.709 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:04:25.709 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:04:25.709 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:04:25.719 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 17:04:25.720 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 17:04:25.720 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 17:04:25.729 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 17:04:25.730 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 17:04:25.732 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 17:04:25.736 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 17:04:25.740 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 17:04:25.743 [Information] LibraryExtractor: Copying system libraries
2025-07-06 17:04:25.751 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 17:04:25.762 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 17:04:53.702 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 17:07:53.067 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 17:09:02.642 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 17:09:55.425 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:10:40.108 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:11:30.738 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:12:20.304 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 17:12:20.305 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 17:12:20.305 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 17:12:20.306 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 17:12:20.306 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 17:12:20.306 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 17:12:20.311 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 17:12:20.314 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 17:12:20.316 [Information] DependencyManager: Initializing dependency manager
2025-07-06 17:12:20.317 [Information] DependencyManager: Setting up library search paths
2025-07-06 17:12:20.318 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:12:20.319 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:12:20.321 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 17:12:20.322 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 17:12:20.323 [Information] DependencyManager: Verifying required directories
2025-07-06 17:12:20.324 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:12:20.324 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:12:20.325 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 17:12:20.326 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 17:12:20.328 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 17:12:20.337 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 17:12:20.338 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 17:12:20.341 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 17:12:20.343 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 17:12:20.344 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 17:12:20.345 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:12:20.346 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:12:20.347 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:12:20.349 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 17:12:20.351 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 17:12:20.352 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 17:12:20.352 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 17:12:20.353 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 17:12:20.353 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 17:12:20.354 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 17:12:20.355 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 17:12:20.355 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 17:12:20.356 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 17:12:20.356 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 17:12:20.357 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 17:12:20.358 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 17:12:20.358 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 17:12:20.359 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 17:12:20.359 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 17:12:20.360 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 17:12:20.360 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 17:12:20.361 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 17:12:20.361 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 17:12:20.362 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 17:12:20.362 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 17:12:20.363 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 17:12:20.363 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 17:12:20.364 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 17:12:20.365 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 17:12:20.366 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 17:12:20.368 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 17:12:20.368 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 17:12:20.369 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 17:12:20.370 [Information] DependencyManager: Setting up environment variables
2025-07-06 17:12:20.370 [Information] DependencyManager: Environment variables configured
2025-07-06 17:12:20.372 [Information] DependencyManager: Verifying library loading status
2025-07-06 17:12:20.691 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 17:12:20.692 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 17:12:20.692 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 17:12:20.695 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 17:12:20.697 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 17:12:20.701 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 17:12:20.704 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 17:12:20.705 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 17:12:20.705 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 17:12:20.707 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 17:12:20.707 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 17:12:20.708 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:12:20.708 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:12:20.709 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 17:12:20.709 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 17:12:20.709 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 17:12:20.710 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:12:20.710 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 17:12:20.710 [Information] App: Integrated startup completed successfully
2025-07-06 17:12:20.714 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 17:12:20.729 [Information] App: Initializing application services
2025-07-06 17:12:20.731 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 17:12:20.733 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 17:12:20.778 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 17:12:20.779 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 17:12:20.780 [Information] App: Configuration service initialized successfully
2025-07-06 17:12:20.782 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 17:12:20.784 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 17:12:20.792 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 17:12:20.792 [Information] App: Final useDummyImplementations value: False
2025-07-06 17:12:20.793 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 17:12:20.794 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 17:12:20.807 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 17:12:20.807 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 17:12:20.808 [Information] App: usePatchedImplementation flag is: True
2025-07-06 17:12:20.808 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 17:12:20.808 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 17:12:20.809 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 17:12:20.809 [Information] App: verboseLogging flag is: True
2025-07-06 17:12:20.811 [Information] App: Verifying real hardware requirements...
2025-07-06 17:12:20.811 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 17:12:20.812 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 17:12:20.812 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 17:12:20.813 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 17:12:20.813 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 17:12:20.813 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 17:12:20.814 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 17:12:20.814 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 17:12:20.826 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 17:12:20.828 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 17:12:20.830 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 17:12:20.833 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 17:12:20.834 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 17:12:20.834 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 17:12:20.835 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 17:12:20.835 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 17:12:20.836 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 17:12:20.836 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 17:12:20.836 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 17:12:20.836 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 17:12:20.838 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 17:12:20.838 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 17:12:20.840 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 17:12:20.841 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 17:12:20.841 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 17:12:20.842 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 17:12:20.844 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 17:12:20.844 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 17:12:20.845 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 17:12:20.847 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:12:20.852 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:12:20.927 [Information] VocomArchitectureBridge: Started bridge process with PID 17804
2025-07-06 17:12:21.964 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 17:12:22.003 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 17:12:22.234 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 17:12:22.235 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:12:22.236 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 17:12:22.236 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 17:12:22.237 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:12:22.237 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:12:22.237 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 17:12:22.238 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:12:22.238 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 17:12:22.238 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 17:12:22.239 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 17:12:22.296 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:12:22.355 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:12:22.358 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:12:22.358 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 17:12:22.363 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 17:12:22.368 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:22.369 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 17:12:22.374 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:12:22.376 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:12:22.377 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:12:22.380 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:12:22.383 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:12:22.387 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:12:22.391 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:12:22.394 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:12:22.404 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:12:22.408 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:12:22.409 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:22.413 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:12:22.413 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:12:22.414 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:22.416 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:12:22.418 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:12:22.418 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:22.421 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:12:22.421 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:12:22.422 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:22.424 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:12:22.424 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:12:22.425 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:22.425 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:12:22.429 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:12:22.431 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:22.431 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:12:22.432 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:12:22.432 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:22.433 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:12:22.433 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:12:22.437 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:22.438 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:12:22.438 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:12:22.439 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:22.439 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:12:22.440 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:12:22.440 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:12:22.442 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:12:22.446 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:12:22.447 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:12:22.448 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:12:22.448 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:12:22.450 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 17:12:23.451 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 17:12:23.451 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:12:23.452 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:12:23.452 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:12:23.453 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:12:23.453 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:12:23.454 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:12:23.455 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:12:23.456 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:12:23.456 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:12:23.456 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:12:23.457 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:23.457 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:12:23.457 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:12:23.458 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:23.458 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:12:23.458 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:12:23.459 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:23.459 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:12:23.459 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:12:23.460 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:23.460 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:12:23.460 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:12:23.461 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:23.461 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:12:23.461 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:12:23.462 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:23.462 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:12:23.462 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:12:23.463 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:23.463 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:12:23.464 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:12:23.464 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:23.464 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:12:23.465 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:12:23.465 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:23.466 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:12:23.466 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:12:23.466 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:12:23.468 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:12:23.469 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:12:23.470 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:12:23.470 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:12:23.470 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:12:23.471 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 17:12:25.471 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 17:12:25.471 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:12:25.472 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:12:25.472 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:12:25.473 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:12:25.473 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:12:25.474 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:12:25.475 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:12:25.476 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:12:25.476 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:12:25.476 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:12:25.477 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:25.477 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:12:25.477 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:12:25.478 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:25.478 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:12:25.478 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:12:25.479 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:25.479 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:12:25.479 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:12:25.480 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:25.480 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:12:25.480 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:12:25.480 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:12:25.481 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:12:25.481 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:12:25.481 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:25.482 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:12:25.482 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:12:25.483 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:25.483 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:12:25.483 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:12:25.484 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:25.484 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:12:25.485 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:12:25.485 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:12:25.486 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:12:25.486 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:12:25.486 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:12:25.486 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:12:25.488 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:12:25.488 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:12:25.488 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:12:25.489 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:12:25.489 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 17:12:28.489 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 17:12:28.489 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 17:12:28.491 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 17:12:28.493 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 17:12:28.994 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 17:12:28.994 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 17:12:28.995 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 17:12:28.996 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 17:12:28.999 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 17:12:29.001 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 17:12:29.005 [Information] BackupService: Initializing backup service
2025-07-06 17:12:29.005 [Information] BackupService: Backup service initialized successfully
2025-07-06 17:12:29.006 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 17:12:29.006 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 17:12:29.009 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 17:12:29.030 [Information] BackupService: Compressing backup data
2025-07-06 17:12:29.040 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-06 17:12:29.041 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 17:12:29.042 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 17:12:29.042 [Information] BackupService: Compressing backup data
2025-07-06 17:12:29.044 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (453 bytes)
2025-07-06 17:12:29.044 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 17:12:29.045 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 17:12:29.045 [Information] BackupService: Compressing backup data
2025-07-06 17:12:29.046 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (450 bytes)
2025-07-06 17:12:29.046 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 17:12:29.047 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 17:12:29.047 [Information] BackupService: Compressing backup data
2025-07-06 17:12:29.048 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-06 17:12:29.048 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 17:12:29.049 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 17:12:29.049 [Information] BackupService: Compressing backup data
2025-07-06 17:12:29.050 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-06 17:12:29.050 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 17:12:29.051 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 17:12:29.051 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 17:12:29.053 [Information] BackupService: Compressing backup data
2025-07-06 17:12:29.054 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (516 bytes)
2025-07-06 17:12:29.054 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 17:12:29.055 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 17:12:29.057 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 17:12:29.061 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 17:12:29.063 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 17:12:29.133 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 17:12:29.134 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 17:12:29.136 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 17:12:29.137 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 17:12:29.137 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 17:12:29.139 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 17:12:29.140 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 17:12:29.145 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 17:12:29.145 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 17:12:29.158 [Information] LicensingService: Initializing licensing service
2025-07-06 17:12:29.203 [Information] LicensingService: License information loaded successfully
2025-07-06 17:12:29.207 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 17:12:29.207 [Information] App: Licensing service initialized successfully
2025-07-06 17:12:29.207 [Information] App: License status: Trial
2025-07-06 17:12:29.208 [Information] App: Trial period: 29 days remaining
2025-07-06 17:12:29.209 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 17:12:29.382 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:12:29.382 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:12:29.383 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 17:12:29.383 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:12:29.433 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 17:12:29.934 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 17:12:29.985 [Information] BackupService: Initializing backup service
2025-07-06 17:12:29.985 [Information] BackupService: Backup service initialized successfully
2025-07-06 17:12:30.036 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 17:12:30.036 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 17:12:30.038 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 17:12:30.038 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 17:12:30.089 [Information] BackupService: Getting predefined backup categories
2025-07-06 17:12:30.140 [Information] MainViewModel: Services initialized successfully
2025-07-06 17:12:30.143 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 17:12:30.145 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:12:30.158 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:12:30.159 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:12:30.160 [Information] MainViewModel: Found 0 Vocom device(s)
