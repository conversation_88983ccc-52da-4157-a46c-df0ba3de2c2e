Log started at 7/6/2025 5:50:55 PM
2025-07-06 17:50:55.850 [Information] LoggingService: Logging service initialized
2025-07-06 17:50:55.869 [Information] App: Starting integrated application initialization
2025-07-06 17:50:55.872 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 17:50:55.875 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 17:50:55.881 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 17:50:55.882 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 17:50:55.885 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 17:50:55.887 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 17:50:55.891 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 17:50:55.903 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:50:55.905 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:50:55.908 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:50:55.910 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 17:50:55.917 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:50:55.919 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:50:55.921 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:50:55.922 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:50:55.935 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:50:55.940 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:50:55.944 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:50:55.945 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:50:55.949 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 17:50:55.952 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:50:55.956 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 17:50:55.956 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:50:55.961 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 17:50:55.964 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 17:50:55.965 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 17:50:55.967 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 17:50:55.967 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 17:50:55.968 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 17:50:55.969 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 17:50:55.969 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 17:50:55.970 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 17:50:55.971 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 17:50:55.971 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 17:50:55.972 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:50:55.972 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:50:55.973 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:50:55.983 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 17:50:55.984 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 17:50:55.985 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 17:50:55.991 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 17:50:55.991 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 17:50:55.995 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 17:50:55.998 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 17:50:56.002 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 17:50:56.004 [Information] LibraryExtractor: Copying system libraries
2025-07-06 17:50:56.010 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 17:50:56.019 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 17:51:24.901 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 17:52:15.533 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 17:53:06.871 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 17:53:53.852 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:54:39.195 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:55:34.515 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:56:24.771 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 17:56:24.771 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 17:56:24.772 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 17:56:24.772 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 17:56:24.772 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 17:56:24.773 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 17:56:24.780 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 17:56:24.782 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 17:56:24.784 [Information] DependencyManager: Initializing dependency manager
2025-07-06 17:56:24.785 [Information] DependencyManager: Setting up library search paths
2025-07-06 17:56:24.786 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:56:24.787 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:56:24.787 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 17:56:24.788 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 17:56:24.789 [Information] DependencyManager: Verifying required directories
2025-07-06 17:56:24.790 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:56:24.790 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:56:24.792 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 17:56:24.793 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 17:56:24.795 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 17:56:24.801 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 17:56:24.804 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 17:56:24.807 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 17:56:24.813 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 17:56:24.814 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 17:56:24.815 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 17:56:24.816 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 17:56:24.817 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 17:56:24.819 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 17:56:24.820 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 17:56:24.821 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 17:56:24.821 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 17:56:24.823 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 17:56:24.825 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 17:56:24.827 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 17:56:24.828 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 17:56:24.828 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 17:56:24.829 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 17:56:24.830 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 17:56:24.831 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 17:56:24.831 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 17:56:24.832 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 17:56:24.832 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 17:56:24.833 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 17:56:24.834 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 17:56:24.835 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 17:56:24.835 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 17:56:24.836 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 17:56:24.836 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 17:56:24.837 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 17:56:24.838 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 17:56:24.838 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 17:56:24.839 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 17:56:24.840 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 17:56:24.844 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 17:56:24.845 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 17:56:24.846 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 17:56:24.847 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 17:56:24.848 [Information] DependencyManager: Setting up environment variables
2025-07-06 17:56:24.848 [Information] DependencyManager: Environment variables configured
2025-07-06 17:56:24.851 [Information] DependencyManager: Verifying library loading status
2025-07-06 17:56:25.251 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 17:56:25.252 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 17:56:25.253 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 17:56:25.255 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 17:56:25.257 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 17:56:25.262 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 17:56:25.263 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 17:56:25.264 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 17:56:25.264 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 17:56:25.266 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 17:56:25.267 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 17:56:25.268 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:56:25.268 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 17:56:25.269 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 17:56:25.269 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 17:56:25.269 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 17:56:25.270 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 17:56:25.270 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 17:56:25.270 [Information] App: Integrated startup completed successfully
2025-07-06 17:56:25.275 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 17:56:25.289 [Information] App: Initializing application services
2025-07-06 17:56:25.292 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 17:56:25.293 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 17:56:25.346 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 17:56:25.347 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 17:56:25.348 [Information] App: Configuration service initialized successfully
2025-07-06 17:56:25.350 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 17:56:25.350 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 17:56:25.357 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 17:56:25.359 [Information] App: Final useDummyImplementations value: False
2025-07-06 17:56:25.360 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 17:56:25.362 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 17:56:25.381 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 17:56:25.383 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 17:56:25.383 [Information] App: usePatchedImplementation flag is: True
2025-07-06 17:56:25.384 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 17:56:25.384 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 17:56:25.384 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 17:56:25.385 [Information] App: verboseLogging flag is: True
2025-07-06 17:56:25.387 [Information] App: Verifying real hardware requirements...
2025-07-06 17:56:25.387 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 17:56:25.388 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 17:56:25.388 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 17:56:25.388 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 17:56:25.389 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 17:56:25.389 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 17:56:25.389 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 17:56:25.390 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 17:56:25.401 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 17:56:25.403 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 17:56:25.405 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 17:56:25.408 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 17:56:25.410 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 17:56:25.410 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 17:56:25.411 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 17:56:25.412 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 17:56:25.412 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 17:56:25.412 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 17:56:25.413 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 17:56:25.414 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 17:56:25.415 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 17:56:25.415 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 17:56:25.417 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 17:56:25.418 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 17:56:25.418 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 17:56:25.418 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 17:56:25.420 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 17:56:25.421 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 17:56:25.422 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 17:56:25.426 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:56:25.430 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:56:25.439 [Information] VocomArchitectureBridge: Started bridge process with PID 17412
2025-07-06 17:56:26.535 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 17:56:26.585 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 17:56:26.746 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 17:56:26.747 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:56:26.747 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 17:56:26.748 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 17:56:26.748 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:56:26.749 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:56:26.749 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 17:56:26.749 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:56:26.750 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 17:56:26.750 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 17:56:26.750 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 17:56:26.811 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:56:27.034 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:56:27.036 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:56:27.036 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 17:56:27.048 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 17:56:27.053 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:27.053 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 17:56:27.059 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:56:27.062 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:56:27.062 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:56:27.066 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:56:27.068 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:56:27.079 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:56:27.082 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:56:27.087 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:56:27.100 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:56:27.104 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:56:27.104 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:27.110 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:56:27.112 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:56:27.113 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:27.115 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:56:27.116 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:56:27.117 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:27.119 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:56:27.120 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:56:27.121 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:27.124 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:56:27.124 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:56:27.125 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:27.126 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:56:27.132 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:56:27.133 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:27.134 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:56:27.134 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:56:27.135 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:27.135 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:56:27.136 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:56:27.137 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:27.137 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:56:27.137 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:56:27.138 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:27.138 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:56:27.139 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:56:27.139 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:56:27.143 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:56:27.162 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:56:27.162 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:56:27.163 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:56:27.164 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:56:27.165 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 17:56:28.167 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 17:56:28.168 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:56:28.169 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:56:28.173 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:56:28.174 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:56:28.179 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:56:28.181 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:56:28.184 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:56:28.187 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:56:28.187 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:56:28.188 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:56:28.189 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:28.189 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:56:28.205 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:56:28.205 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:28.206 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:56:28.206 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:56:28.206 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:28.207 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:56:28.207 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:56:28.207 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:28.209 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:56:28.210 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:56:28.210 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:28.211 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:56:28.211 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:56:28.211 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:28.212 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:56:28.212 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:56:28.212 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:28.213 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:56:28.213 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:56:28.214 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:28.214 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:56:28.215 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:56:28.215 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:28.216 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:56:28.216 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:56:28.216 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:56:28.217 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:56:28.234 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:56:28.235 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:56:28.235 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:56:28.236 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:56:28.236 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 17:56:30.237 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 17:56:30.239 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 17:56:30.239 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 17:56:30.240 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 17:56:30.240 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 17:56:30.241 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 17:56:30.242 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 17:56:30.242 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 17:56:30.244 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 17:56:30.244 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:56:30.245 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 17:56:30.245 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:30.245 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:56:30.246 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 17:56:30.246 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:30.246 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:56:30.246 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 17:56:30.248 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:30.248 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:56:30.248 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 17:56:30.249 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:30.249 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:56:30.250 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 17:56:30.251 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 17:56:30.251 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 17:56:30.252 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 17:56:30.253 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:30.253 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 17:56:30.253 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 17:56:30.254 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:30.254 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 17:56:30.254 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 17:56:30.255 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:30.255 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 17:56:30.255 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 17:56:30.256 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 17:56:30.256 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 17:56:30.256 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 17:56:30.257 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 17:56:30.257 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:56:30.263 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:56:30.263 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:56:30.264 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 17:56:30.264 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 17:56:30.264 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 17:56:33.269 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 17:56:33.271 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 17:56:33.272 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 17:56:33.274 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 17:56:33.775 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 17:56:33.779 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 17:56:33.781 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 17:56:33.781 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 17:56:33.786 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 17:56:33.788 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 17:56:33.796 [Information] BackupService: Initializing backup service
2025-07-06 17:56:33.797 [Information] BackupService: Backup service initialized successfully
2025-07-06 17:56:33.797 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 17:56:33.798 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 17:56:33.805 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 17:56:33.855 [Information] BackupService: Compressing backup data
2025-07-06 17:56:33.873 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-06 17:56:33.875 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 17:56:33.875 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 17:56:33.876 [Information] BackupService: Compressing backup data
2025-07-06 17:56:33.879 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-06 17:56:33.881 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 17:56:33.882 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 17:56:33.882 [Information] BackupService: Compressing backup data
2025-07-06 17:56:33.884 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-06 17:56:33.884 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 17:56:33.885 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 17:56:33.885 [Information] BackupService: Compressing backup data
2025-07-06 17:56:33.886 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-06 17:56:33.887 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 17:56:33.929 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 17:56:33.951 [Information] BackupService: Compressing backup data
2025-07-06 17:56:33.953 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-06 17:56:33.953 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 17:56:33.953 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 17:56:33.954 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 17:56:33.955 [Information] BackupService: Compressing backup data
2025-07-06 17:56:33.956 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-06 17:56:33.957 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 17:56:33.959 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 17:56:33.965 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 17:56:33.969 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 17:56:33.973 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 17:56:34.117 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 17:56:34.119 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 17:56:34.121 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 17:56:34.121 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 17:56:34.122 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 17:56:34.123 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 17:56:34.124 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 17:56:34.131 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 17:56:34.131 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 17:56:34.146 [Information] LicensingService: Initializing licensing service
2025-07-06 17:56:34.359 [Information] LicensingService: License information loaded successfully
2025-07-06 17:56:34.368 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 17:56:34.374 [Information] App: Licensing service initialized successfully
2025-07-06 17:56:34.384 [Information] App: License status: Trial
2025-07-06 17:56:34.385 [Information] App: Trial period: 29 days remaining
2025-07-06 17:56:34.386 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 17:56:34.656 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 17:56:34.656 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 17:56:34.657 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 17:56:34.657 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 17:56:34.707 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 17:56:35.259 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 17:56:35.336 [Information] BackupService: Initializing backup service
2025-07-06 17:56:35.337 [Information] BackupService: Backup service initialized successfully
2025-07-06 17:56:35.388 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 17:56:35.389 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 17:56:35.391 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 17:56:35.391 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 17:56:35.442 [Information] BackupService: Getting predefined backup categories
2025-07-06 17:56:35.494 [Information] MainViewModel: Services initialized successfully
2025-07-06 17:56:35.497 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 17:56:35.499 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 17:56:35.511 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 17:56:35.513 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 17:56:35.514 [Information] MainViewModel: Found 0 Vocom device(s)
